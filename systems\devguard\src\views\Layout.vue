<template>
  <div class="devguard-layout h-full flex flex-col">
    <ForgeonHeader
      class="flex-none"
      :onHandleMenuExpand="onHandleMenuExpand"
      :showUnfoldIcon="showUnfoldIcon"
      title="DevGuard - 提交中心"
    >
      <template #actions>
        <Announcement v-if="isAnnouncementShow" />

        <ForgeonProjectSelector
          v-if="isProjectSelectorShow"
          class="w-[160px]"
          containerClass="bg-FO-Container-Fill2"
          :options="forgeonConfig.projectList"
          :value="forgeonConfig.currentProjectId"
          @select="handleProjectSelect"
        />
      </template>
    </ForgeonHeader>
    <router-view />
  </div>
</template>

<script lang="ts" setup>
import { ForgeonHeader, ForgeonProjectSelector, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { useForgeonConfigStore } from '../store/modules/forgeonConfig';
import { store } from '../store/pinia';
import Announcement from './announcement/Announcement.vue';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const forgeonConfig = useForgeonConfigStore(store);
const platformConfig = computed(() => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    return useMicroAppInject(usePlatformConfigCtx);
  } else {
    return null;
  }
});

const showUnfoldIcon = computed(() => {
  if (platformConfig.value?.data.value) {
    return !platformConfig.value.data.value.isMenuExpanded;
  } else {
    return false;
  }
});

const isProjectSelectorShow = computed(() => {
  return router.currentRoute.value.meta.isProjectSelectorShow;
});
const isAnnouncementShow = computed(() => {
  return router.currentRoute.value.meta.isAnnouncementShow || false;
});

function onHandleMenuExpand() {
  platformConfig.value?.data.value?.changeMenuExpendStatus(true);
}
function handleProjectSelect(id: number | undefined) {
  if (id !== undefined) {
    forgeonConfig.setCurrentProjectId(id);
  }
}
</script>
