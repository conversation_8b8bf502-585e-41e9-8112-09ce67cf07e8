<template>
  <div class="mb-10px b-rd-8px bg-FO-Container-Fill3 p-10px">
    <div class="flex justify-between">
      <div v-if=" item?.triggerType === TriggerType.Periodic" class="FO-Font-B14">
        每周{{ trueIndicesToChinese(item?.cycleWeekdays!).join('、') }} {{ dayjs(item.startTimestamp).format('HH:mm:ss') }} - {{ dayjs(item.endTimestamp).format('HH:mm:ss') }}
      </div>
      <div v-else class="FO-Font-B14">
        {{ dayjs(item.startTimestamp).format('YYYY/MM/DD HH:mm:ss') }} - {{ dayjs(item.endTimestamp).format('YYYY/MM/DD HH:mm:ss') }}
      </div>
      <div class="flex gap-10px">
        <Button v-if="announcementType === 'new'" type="primary" size="small" @click="handleEdit(item)">
          修改
        </Button>
        <Popconfirm
          v-if="announcementType === 'new'"
          :title="`确定要${item.triggerType === TriggerType.Once ? '  删除' : '停止'}该公告吗?`"
          okText="确认"
          cancelText="取消"
          @confirm="handleDelete(item)"
        >
          <Button type="primary" size="small" class="bg-FO-Container-Stroke5!">
            {{ item.triggerType === TriggerType.Once ? '  删除' : '停止' }}
          </Button>
        </Popconfirm>
        <Button v-if="announcementType === 'history'" type="primary" size="small" @click="handleReuse(item)">
          复用
        </Button>
      </div>
    </div>
    <div class="m-10px flex items-center justify-center gap-4px b-rd-4px bg-FO-Container-Fill1 p-8px" :class="{ 'c-FO-Functional-Warning1-Default': item.inEffect }">
      <Icon :icon="broadcastIcon" />
      <span>{{ item?.modifier?.nickName || item?.usernameCn }}：{{ item?.content }}</span>
    </div>
    <div class="FO-Font-R12">
      适用分支： {{ streamsList?.filter((e) => item?.streamIDs?.includes(e.submitStreamID)).map((e) => e.description).join('、') }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { Button, Popconfirm } from 'ant-design-vue';
import broadcastIcon from '@iconify-icons/icon-park-outline/broadcast';
import { type AnnouncementsItem, type StreamsListItem, deleteAnnouncementsHistoryListApi } from '../../api';
import dayjs from 'dayjs';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { computed } from 'vue';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { TriggerType } from './annnouncement.data';

defineProps<{
  item: AnnouncementsItem ;
  streamsList: StreamsListItem[];
  announcementType: string;
}>();
const emit = defineEmits<{
  (e: 'editAnnouncement', item: AnnouncementsItem): void;
  (e: 'reuseAnnouncement', item: AnnouncementsItem): void;
  (e: 'deleteAnnouncement', item: AnnouncementsItem): void;
}>();
const { execute: deleteAnnouncementsHistoryListExecute } = useLatestPromise(deleteAnnouncementsHistoryListApi);
const forgeonConfig = useForgeonConfigStore(store);
const currentProjectId = computed(() => forgeonConfig.currentProjectId);
function trueIndicesToChinese(arr: boolean[]) {
  if (!arr) {
    return [];
  }
  const numToChinese = ['一', '二', '三', '四', '五', '六', '日'];

  return arr
    .map((val: boolean, idx: number) => (val ? numToChinese[idx] : null))
    .filter((item: string | null) => item !== null);
}
async function handleDelete(item: AnnouncementsItem) {
  await deleteAnnouncementsHistoryListExecute({ id: currentProjectId.value! }, { recordID: item.ID! });
  emit('deleteAnnouncement', item);
}
function handleEdit(item: AnnouncementsItem) {
  emit('editAnnouncement', item);
}

function handleReuse(item: AnnouncementsItem) {
  emit('reuseAnnouncement', item);
}
</script>

<style lang="less">

</style>
