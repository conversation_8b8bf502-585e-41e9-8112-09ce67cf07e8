{"name": "@hg-tech/devguard", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/api-schema-merge": "^1.0.41", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@iconify-icons/ant-design": "catalog:", "@iconify-icons/ic": "catalog:", "@iconify-icons/icon-park-outline": "catalog:", "@iconify-icons/line-md": "catalog:", "@iconify/vue": "catalog:", "@micro-zoe/micro-app": "catalog:", "@vueuse/core": "catalog:", "@vueuse/router": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "catalog:", "pinia": "catalog:", "sortablejs": "catalog:", "unocss": "catalog:", "uuid": "catalog:", "vue": "catalog:", "vue-request": "^2.0.4", "vue-router": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "catalog:", "@types/sortablejs": "catalog:", "@types/uuid": "catalog:", "cross-env": "catalog:", "vite": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:"}}