<template>
  <div ref="containerRef" class="flex flex-col">
    <div v-for="item in commitList" :key="item.ID" ref="itemRefs" class="c-FO-Functional-Warning1-Default line-height-20px">
      {{ item ? ` ${item.usernameCn}：${item.content}` : '' }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { showAnnouncementsItem } from '../../api';
import { nextTick, ref, watch } from 'vue';

const props = withDefaults(defineProps<{ nowAnnouncementsItem: showAnnouncementsItem | undefined }>(), { nowAnnouncementsItem: undefined });
const containerRef = ref<HTMLElement>();
const itemRefs = ref<HTMLElement[]>([]);
const commitList = ref<showAnnouncementsItem[]>([]);

function scrollChildToBottom() {
  const parent = containerRef.value;
  const children = itemRefs.value;
  if (!parent || !children.length) {
    return;
  }
  const lastChild = children[children.length - 1];
  lastChild.scrollIntoView({ behavior: 'smooth', block: 'end' });
}
watch(() => props.nowAnnouncementsItem, () => {
  if (!props.nowAnnouncementsItem) {
    commitList.value = [];
    scrollChildToBottom();
    return;
  }
  commitList.value.push(props.nowAnnouncementsItem);
  nextTick(() => {
    scrollChildToBottom();
  });
  if (commitList.value.length > 3) {
    commitList.value = commitList.value.slice(-2);
  }
}, { immediate: true, deep: true });
</script>

<style lang="less" scoped>
</style>
