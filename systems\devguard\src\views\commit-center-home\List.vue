<template>
  <div class="commit-center flex flex-col overflow-auto">
    <Spin :spinning="submitListLoading">
      <div v-for="depot in depotsList" :key="depot.prefix" class="commit-center-card">
        <div class="flex items-center">
          <TypographyText :ellipsis="{ tooltip: true }" :content="depot.name || depot.prefix" class="FO-Font-B16 mb-4 max-w-300px!" />
        </div>
        <StreamsList :streamsList="depot.streams" :nowAnnouncementsList="nowAnnouncementsList" />
      </div>

      <div v-if="!depotsList?.length" class="mt-300px flex items-center justify-center">
        <Empty description="暂无分支数据" />
      </div>
    </Spin>
  </div>
</template>

<script lang="ts" setup>
import { Empty, Spin, TypographyText } from 'ant-design-vue';
import { computed, onUnmounted, ref, watch } from 'vue';
import { type ListSubmitItem, type showAnnouncementsItem, batchQueryAnnouncementsApi, getListSubmit } from '../../api';
import StreamsList from './StreamsList.vue';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { useRequest } from 'vue-request';

const forgeonConfig = useForgeonConfigStore(store);
const { execute, data: submitList, loading: submitListLoading } = useLatestPromise(getListSubmit);
const { execute: batchQueryAnnouncementsExecute, data: batchQueryAnnouncementsRes } = useLatestPromise(batchQueryAnnouncementsApi);
const cancel = ref();
const currentProjectId = computed(() => forgeonConfig.currentProjectId);
const projectCode = computed(() => forgeonConfig.currentProjectInfo?.alias);
const depotsList = ref<ListSubmitItem[]>();
const nowAnnouncementsListParams = ref<{ streamID: number; recordID: number }[]>([]);
const nowAnnouncementsList = ref<showAnnouncementsItem[]>([]);

async function getBatchQueryAnnouncements() {
  await batchQueryAnnouncementsExecute({ id: currentProjectId.value! }, { streamList: nowAnnouncementsListParams.value });
  nowAnnouncementsList.value = batchQueryAnnouncementsRes.value?.data?.data || [];
  nowAnnouncementsListParams.value = [];
  depotsList.value?.forEach((item) => {
    item.streams.forEach((stream) => {
      nowAnnouncementsListParams.value.push({
        streamID: stream.submitStreamID,
        recordID: nowAnnouncementsList.value.find((item) => item?.streamID === stream.submitStreamID)?.ID || 0,
      });
    });
  });
}

watch(() => [currentProjectId.value, projectCode.value], async () => {
  if (!currentProjectId.value || !projectCode.value) {
    return;
  }
  await execute({ projectCode: projectCode.value, id: currentProjectId.value }, { });
  depotsList.value = submitList.value?.data?.data?.list;
  depotsList.value?.forEach((item) => {
    item.streams.forEach((stream) => {
      nowAnnouncementsListParams.value.push({
        streamID: stream.submitStreamID,
        recordID: 0,
      });
    });
  });
  const { cancel } = useRequest(getBatchQueryAnnouncements, {
    pollingInterval: 10000,
  });
  cancel.value = cancel;
}, { immediate: true });

onUnmounted(() => {
  cancel.value?.();
});
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.commit-center {
  &-card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
    margin: 20px;

    &-list {
      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 16px;
        border: 1px solid @FO-Container-Stroke1;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;

        &:hover {
          border-color: @FO-Brand-Primary-Default;
        }

        &:not(:last-child) {
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
